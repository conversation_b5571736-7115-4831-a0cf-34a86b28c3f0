/* EventBus 聚合插件 */
// 功能: 按需导入并安装所有事件类插件

import type { App } from 'vue'
import { httpEventBus } from '@/utils/http'
import { useToast } from '@/composables/useToast'
import { useAuthStore } from '@/stores/auth'
import { type Router } from 'vue-router'

// 存储清理函数，在应用卸载时调用
let cleanupHandlers: (() => void)[] = []

// 全局Token状态管理
let isTokenRefreshing = false
let refreshCount = 0

export default (app: App) => {
  
  // 获取路由实例，需要在 app 挂载后才能安全使用
  let router: Router | null = null
  
  // 延迟获取路由实例
  app.mixin({
    beforeCreate() {
      if (!router && this.$router) {
        router = this.$router
        setupHttpEventHandlers(router)
        setupTokenEventHandlers(router)
      }
    }
  })

  // 在应用卸载时清理事件监听器
  const originalUnmount = app.unmount
  app.unmount = function() {
    cleanupHandlers.forEach(cleanup => cleanup())
    cleanupHandlers = []
    originalUnmount.call(this)
  }
}

function setupHttpEventHandlers(router: Router) {
  const toast = useToast()

  // 请求错误统一提示
  const stopError = httpEventBus.on('request:error', (e) => {
    // 根据错误类型显示不同的提示
    switch (e.type) {
      case 'network':
        toast.error('网络连接异常，请检查网络设置')
        break
      case 'timeout':
        toast.error('请求超时，请稍后重试')
        break
      case 'business':
        // 业务错误，显示后端返回的错误信息
        if (e.message && e.code !== 401001 && e.code !== 401002) {
          toast.error(e.message)
        }
        break
      case 'http':
        toast.error(`请求失败 (${e.status})`)
        break
      default:
        toast.error('请求失败，请稍后重试')
    }
  })

  // Token 失效 → 跳转授权页
  const stopAuth = httpEventBus.on('auth:logout', () => {
    toast.warning('登录已过期，请重新登录')
    // 统一回到首页，触发路由守卫重新鉴权
    router.replace('/')
  })

  // 慢接口监控 (>2s)
  const stopSlow = httpEventBus.on('request:success', (e) => {
    if (e.duration > 2000) {
      console.info(`[慢接口] ${e.url} 耗时: ${e.duration}ms`)
    }
  })

  // 重试事件监控
  const stopRetry = httpEventBus.on('request:retry', (e) => {
    console.info(`[请求重试] ${e.url} 第${e.retryCount}次重试 (${e.reason})`)
  })

  // 存储清理函数
  cleanupHandlers.push(stopError, stopAuth, stopSlow, stopRetry)
}

function setupTokenEventHandlers(router: Router) {
  const toast = useToast()
  const authStore = useAuthStore()

  // Token 状态变化监听
  const stopTokenStatus = httpEventBus.on('token:status', (event) => {
    console.log('[Token监控]', event.type, {
      hasAccessToken: event.hasAccessToken,
      hasRefreshToken: event.hasRefreshToken,
      isAccessTokenExpired: event.isAccessTokenExpired,
      isRefreshTokenExpired: event.isRefreshTokenExpired
    })

    switch (event.type) {
      case 'no_token':
        console.warn('[Token监控] 无Token，需要登录')
        handleNoToken(router, authStore)
        break
        
      case 'refresh_expired':
        console.warn('[Token监控] RefreshToken已过期，需要重新认证')
        handleRefreshExpired(router, authStore, toast)
        break
        
      case 'refreshing':
        console.log('[Token监控] 正在刷新Token...')
        isTokenRefreshing = true
        // Token刷新无感，不显示任何UI提示
        break
        
      case 'refreshed':
        console.log('[Token监控] Token刷新成功')
        isTokenRefreshing = false
        refreshCount++
        // Token刷新成功，无感处理，不显示任何提示
        break
        
      case 'refresh_failed':
        console.error('[Token监控] Token刷新失败')
        isTokenRefreshing = false
        handleRefreshFailed(router, authStore, toast)
        break
    }
  })

  // 存储清理函数
  cleanupHandlers.push(stopTokenStatus)
}

// Token 状态处理函数
function handleNoToken(router: Router, authStore: any) {
  authStore.setNeedsReauth(true)
  // 可以选择跳转到登录页或触发微信授权
  if (router.currentRoute.value.meta.auth !== false) {
    router.push('/auth/login')
  }
}

function handleRefreshExpired(router: Router, authStore: any, toast: any) {
  authStore.logout()
  authStore.setNeedsReauth(true)
  toast.warning('登录已过期，请重新登录')
  // 跳转到登录页
  if (router.currentRoute.value.meta.auth !== false) {
    router.push('/auth/login')
  }
}

function handleRefreshFailed(router: Router, authStore: any, toast: any) {
  authStore.logout()
  authStore.setNeedsReauth(true)
  toast.error('登录状态异常，请重新登录')
  // 可以选择重试或跳转登录页
  if (router.currentRoute.value.meta.auth !== false) {
    router.push('/auth/login')
  }
}

// 导出全局状态，供其他地方使用
export function getTokenRefreshingStatus() {
  return {
    isRefreshing: isTokenRefreshing,
    refreshCount
  }
}

// 手动触发Token状态检查
export function checkTokenStatus() {
  const authStore = useAuthStore()
  const event = {
    type: authStore.isRefreshTokenExpired ? 'refresh_expired' : 
          authStore.isAccessTokenExpired ? 'refreshing' : 'refreshed',
    hasAccessToken: !!authStore.accessToken,
    hasRefreshToken: !!authStore.refreshToken,
    isAccessTokenExpired: authStore.isAccessTokenExpired,
    isRefreshTokenExpired: authStore.isRefreshTokenExpired,
    timestamp: Date.now()
  }
  
  // 手动触发事件
  httpEventBus.emit('token:status', event)
}
