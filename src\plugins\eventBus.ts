/* EventBus 聚合插件 */
// 功能: 按需导入并安装所有事件类插件

import type { App } from 'vue'
import { httpEventBus } from '@/utils/http'
import { useToast } from '@/composables/useToast'
import { useAuthStore } from '@/stores/auth'
import { type Router } from 'vue-router'

// 存储清理函数，在应用卸载时调用
let cleanupHandlers: (() => void)[] = []

export default (app: App) => {

  // 获取路由实例，需要在 app 挂载后才能安全使用
  let router: Router | null = null

  // 延迟获取路由实例
  app.mixin({
    beforeCreate() {
      if (!router && this.$router) {
        router = this.$router
        setupHttpEventHandlers(router)
      }
    }
  })

  // 在应用卸载时清理事件监听器
  const originalUnmount = app.unmount
  app.unmount = function() {
    cleanupHandlers.forEach(cleanup => cleanup())
    cleanupHandlers = []
    originalUnmount.call(this)
  }
}

function setupHttpEventHandlers(router: Router) {
  const toast = useToast()

  // 请求错误统一提示
  const stopError = httpEventBus.on('request:error', (e) => {
    // 根据错误类型显示不同的提示
    switch (e.type) {
      case 'network':
        toast.error('网络连接异常，请检查网络设置')
        break
      case 'timeout':
        toast.error('请求超时，请稍后重试')
        break
      case 'business':
        // 业务错误，显示后端返回的错误信息
        // 过滤掉所有token相关的错误码，保持认证过程无感知
        const tokenErrorCodes = [401001, 401002, 500002] // Token无效、Token过期、refresh_token无效
        if (e.message && !tokenErrorCodes.includes(e.code)) {
          toast.error(e.message)
        }
        break
      case 'http':
        toast.error(`请求失败 (${e.status})`)
        break
      default:
        toast.error('请求失败，请稍后重试')
    }
  })

  // Token 失效 → 无感知跳转重新认证
  const stopAuth = httpEventBus.on('auth:logout', () => {
    // 无感知重新认证：直接跳转到首页，由路由守卫处理微信授权
    // 不显示任何用户提示，让整个过程对用户透明
    router.replace('/')
  })

  // 慢接口监控 (>2s)
  const stopSlow = httpEventBus.on('request:success', (e) => {
    if (e.duration > 2000) {
      console.info(`[慢接口] ${e.url} 耗时: ${e.duration}ms`)
    }
  })

  // 重试事件监控
  const stopRetry = httpEventBus.on('request:retry', (e) => {
    console.info(`[请求重试] ${e.url} 第${e.retryCount}次重试 (${e.reason})`)
  })

  // 存储清理函数
  cleanupHandlers.push(stopError, stopAuth, stopSlow, stopRetry)
}

// 移除复杂的Token事件处理，简化为只处理核心的认证失效事件
