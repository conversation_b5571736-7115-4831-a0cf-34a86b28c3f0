# API 交互规范 (@vue-request & http)

本规则 **强制** 规定项目 API 定义、网络请求封装 (`@vue-request`) 及状态管理的最佳实践。
**MUST**: 严格遵循以下规范处理所有前后端数据交互。

## API 定义 (`api.ts`)

*   **MUST**: API 端点定义 **必须** 放置在相应的 `api.ts` 文件中。
    *   全局 API: `src/api/module-name/api.ts` (例如 `src/api/user/api.ts`)
    *   页面级 API: `src/pages/feature-name/api.ts`

### 推荐使用方式（简洁版）

*   **RECOMMENDED**: 优先使用新的简洁 API 方式，减少层级和复杂度。
    ```typescript
    // src/api/user/api.ts - 简洁方式
    import { get, post, put, del } from '~/utils/http'
    import type { User, CreateUserData } from './types'

    export const userApi = {
      // 获取用户列表
      getUsers: () => get<User[]>('/api/users'),
      
      // 获取单个用户
      getUser: (id: number) => get<User>(`/api/users/${id}`),
      
      // 创建用户
      createUser: (data: CreateUserData) => post<User>('/api/users', data),
      
      // 更新用户
      updateUser: (id: number, data: Partial<CreateUserData>) => 
        put<User>(`/api/users/${id}`, data),
      
      // 删除用户
      deleteUser: (id: number) => del(`/api/users/${id}`)
    }
    ```

*   **RECOMMENDED**: 对于复杂请求，使用 `request` 方法（类似 axios）。
    ```typescript
    // 复杂请求示例
    export const complexApi = {
      // 带查询参数
      searchUsers: (query: string, page: number = 1) => 
        get<User[]>('/api/users/search', {
          params: { q: query, page }
        }),
      
      // 带自定义头部
      uploadFile: (file: File) => {
        const formData = new FormData()
        formData.append('file', file)
        return post<{ url: string }>('/api/upload', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        })
      },
      
      // 通用请求方法
      customRequest: (data: any) => 
        request<ResponseType>({
          url: '/api/custom',
          method: 'POST',
          data,
          headers: { 'X-Custom': 'value' }
        })
    }
    ```

### 传统使用方式（保持兼容）

*   **MUST**: 使用 `src/utils/http` 中封装的 `http` 实例和辅助函数 (如 `createApi`) 来定义 API。
    ```typescript
    // src/api/game/api.ts
    import { http, createApi } from '~/utils/http'
    import type { Game, GameListParams } from './types' // 明确的类型定义

    // 使用 createApi 组织相关接口 (推荐)
    export const gameApi = createApi({
      getGameList: (params: GameListParams) => http.get<Game[]>('/api/v1/games', params),
      getGameDetail: (gameId: string) => http.get<Game>(`/api/v1/games/${gameId}`),
    });

    // 或者直接导出单个 API 函数
    export function reportGameAction(data: { action: string; gameId: string }) {
      return http.post('/api/v1/game-action', data);
    }
    ```

### 公开 API 定义

*   **MUST**: 对于无需认证的公开接口，**必须** 使用公开方法。
    ```typescript
    // src/api/public/api.ts - 简洁方式
    import { publicGet, publicPost } from '~/utils/http'
    import type { PublicConfig, SystemStatus } from './types'

    export const publicApi = {
      getConfig: () => publicGet<PublicConfig>('/api/public/config'),
      getStatus: () => publicGet<SystemStatus>('/api/public/status'),
      submitFeedback: (data: FeedbackData) => 
        publicPost<{ success: boolean }>('/api/public/feedback', data),
    };
    ```

*   **CRITICAL**: 在 `createPublicApi` 中定义的 API 函数 **必须** 使用 `publicApi.get/post/put/delete` 方法，**禁止** 使用 `http.get/post/put/delete` 方法。
    ```typescript
    // ❌ 错误示例 - 仍会包含 accessToken
    export const wrongApi = createPublicApi({
      getData: () => http.get('/api/public/data') // 错误！
    })

    // ✅ 正确示例 - 不会包含 accessToken
    export const correctApi = createPublicApi({
      getData: () => publicApi.get('/api/public/data') // 正确！
    })
    ```

*   **MUST**: 为 API 的请求参数 (`params`, `data`) 和响应数据 (`http.get<ResponseType>`) 提供明确的 TypeScript 类型定义。
*   **INFO**: 详细的 `http` 工具用法参考 [HTTP 工具 README](mdc:src/utils/http/README.md)。

## 网络请求执行 (`composables`)

*   **MUST**: **所有** 的网络请求 **必须** 通过 `@vue-request` 库来执行。**禁止** 直接调用 `http.get/post` 等方法。
*   **MUST**: 网络请求的发起和相关逻辑（如数据转换、状态管理）**必须** 封装在 `composables` 函数中。
    *   **AVOID**: 直接在 Vue 组件 (`.vue`) 的 `<script setup>` 中调用 `useRequest` 或 `api.ts` 中的函数。
    ```typescript
    // src/pages/game/list/composables/useGameList.ts
    import { ref, computed } from 'vue'
    import { useRequest } from 'vue-request'
    import { gameApi } from '~/api/game/api' // 引入全局或页面 API
    import type { Game, GameListParams } from '~/api/game/types'

    export function useGameList() {
      const params = ref<GameListParams>({ page: 1, pageSize: 10, category: 'all' });

      const { data, loading, error, run, refresh } = useRequest(
        () => gameApi.getGameList(params.value),
        {
          // manual: true, // 如果需要手动触发初始请求
          // refreshDeps: [params], // 如果 params 是响应式对象，应考虑使用 refreshDeps 或 watch
          debounceInterval: 300,
          onError: (e) => {
            // MUST: 实现统一或局部的错误处理逻辑
            console.error('Failed to fetch game list:', e);
            // 可在此处调用全局错误提示，例如 Varlet Snackbar
          }
        }
      );

      // 封装参数变更和重新请求的逻辑
      function changeCategory(category: string) {
        params.value = { ...params.value, category, page: 1 };
        run(params.value); // 或者 refresh()，取决于 useRequest 配置
      }

      // 可以对返回数据进行转换或计算
      const gameList = computed(() => data.value?.data || []); // 假设 API 返回 { data: [...] }
      const totalGames = computed(() => data.value?.total || 0);

      return {
        gameList,
        totalGames,
        loading,
        error,
        params,
        changeCategory,
        refresh, // 暴露 refresh 方法供外部调用
      };
    }
    ```

## 状态管理与错误处理

*   **MUST**: **优先** 使用 `useRequest` 返回的 `data`, `loading`, `error` 响应式引用来管理请求的 数据、加载中、错误 状态。
*   **AVOID**: 在组件中自行创建 `ref` 来跟踪 `loading` 或 `error` 状态。
*   **MUST**: 在 `useRequest` 的 `options` 中或 `composables` 内部实现必要的错误处理逻辑 (`onError` 回调)。
    *   **SHOULD**: 考虑使用全局错误处理机制（例如 `http` 工具的拦截器或 Vue 插件）处理通用错误（如 401 未授权，5xx 服务器错误）。
    *   **SHOULD**: 在 `onError` 中根据具体业务场景进行反馈（如 Varlet Snackbar 提示）。
*   **MUST**: 在模板中使用 `loading` 和 `error` 状态，提供用户友好的反馈（加载提示、错误信息、空状态）。
    ```vue
    <template>
      <LoadingIndicator v-if="loading" />
      <ErrorMessage v-else-if="error" :message="error.message" @retry="refresh" />
      <GameList v-else-if="gameList.length > 0" :games="gameList" />
      <EmptyState v-else />
    </template>
    ```
      <ErrorMessage v-else-if="error" :message="error.message" @retry="refresh" />
      <GameList v-else-if="gameList.length > 0" :games="gameList" />
      <EmptyState v-else />
    </template>
    ```