import { request } from '@/utils/http'
import type { LoginParams, TokenInfo, UserInfo } from './types'

const DEFAULT_PREFIX = '/operation-auth-service/auth/weunit'
// 微信统一认证相关接口
export const weunitAuthApi = {
  // 登录接口
  login: (params: LoginParams) => 
    request<TokenInfo>({
      url: `${DEFAULT_PREFIX}/login`,
      method: 'POST',
      data: params,
    }),
  
  // 刷新令牌接口
  refreshToken: (refreshToken: string) => 
    request<TokenInfo>({
      url: `${DEFAULT_PREFIX}/refresh`,
      method: 'POST',
      data: { refreshToken },
    }),
  
  // 校验令牌接口
  checkToken: () => 
    request<Omit<UserInfo, 'id'>>({
      url: `${DEFAULT_PREFIX}/check-token`,
      method: 'POST',
    }),
}

export default weunitAuthApi 