import { request } from '@/utils/http'
import type { LoginParams, TokenInfo, UserInfo } from './types'

const DEFAULT_PREFIX = '/operation-auth-service/auth/weunit'

// 微信统一认证相关接口 - 使用新的简化API调用方式
export const weunitAuthApi = {
  // 登录接口 - 无需认证
  login: (params: LoginParams) =>
    request<TokenInfo>({
      url: `${DEFAULT_PREFIX}/login`,
      method: 'POST',
      data: params,
      auth: false  // 登录接口无需认证
    }),

  // 刷新令牌接口 - 无需认证
  refreshToken: (refreshToken: string) =>
    request<TokenInfo>({
      url: `${DEFAULT_PREFIX}/refresh`,
      method: 'POST',
      data: { refreshToken },
      auth: false  // 刷新接口无需认证
    }),

  // 校验令牌接口 - 需要认证（默认）
  checkToken: () =>
    request<Omit<UserInfo, 'id'>>({
      url: `${DEFAULT_PREFIX}/check-token`,
      method: 'POST',
      // auth: true 是默认值，可以省略
    }),
}

export default weunitAuthApi 