import { createGetRoutes, setupLayouts } from 'virtual:meta-layouts'
import { createRouter, createWebHistory, type Router } from 'vue-router'
import { routes as fileRoutes } from 'vue-router/auto-routes'
import NProgress from 'nprogress'
import { useAuthStore, LoginStatus } from '@/stores/auth'
import { createAuthHandler } from '@/middleware/auth'
import { createRedirectMiddleware } from '@/middleware/redirect'
import { safeResolve } from '@/composables/path'

declare module 'vue-router' {
	// 在这里定义你的 meta 类型
	// eslint-disable-next-line no-unused-vars
	interface RouteMeta {
		title?: string
		layout?: string
	}
}

// 重定向 BASE_URL
fileRoutes.flat(Infinity).forEach((route) => {
	route.path = safeResolve(route.path)
})

export const router = createRouter({
	history: createWebHistory(),
	routes: setupLayouts(fileRoutes),
})

export const getRoutes = createGetRoutes(router)

function setupRouterGuards(router: Router) {


	const handleAuth = createAuthHandler({
		appId: import.meta.env.VITE_APP_WECHAT_APP_ID,
		webId: import.meta.env.VITE_APP_WECHAT_WEB_ID,
	})

	// 创建URL重定向中间件
	const handleRedirect = createRedirectMiddleware()

	router.beforeEach(async (to, from, next) => {
		// 不需要认证的页面直接通过
		if (to.meta.auth === false) {
			next()
			return
		}
		// 应用URL重定向中间件
		const redirectTarget = handleRedirect(to)
		if (redirectTarget) {
			next(redirectTarget)
			return
		}

		const authStore = useAuthStore()
		const authInProgress =
			sessionStorage.getItem('__giant__wx__authInProgress') === 'true'

		// 开发环境：跳过微信 OAuth，直接使用固定 code 自动登录
		if (import.meta.env.DEV) {
			if (!authStore.isLoggedIn) {
				try {
					await authStore.handleLogin({ code: 'VALID_CODE' })
				} catch (error) {
					/* eslint-disable no-console */
					console.warn('[DEV] 自动登录失败:', error)
				}
			}
			next()
			return
		}

		// 需要重新认证或首次访问需要认证的页面
		if (authStore.needsReauth || authStore.loginStatus === LoginStatus.NOT_LOGGED) {
			if (!authInProgress) authStore.setLoginStatus(LoginStatus.NOT_LOGGED)
			await handleAuth(to, next)
			return
		}

		// 其他情况，继续处理认证流程
		await handleAuth(to, next)
	})

	router.afterEach(() => {
		NProgress.done()
	})
}

setupRouterGuards(router)

export default router
