/* httpNew 对外导出入口 */
// 功能: 提供默认 http 实例、工厂方法及 API 快速包装工具

import { HttpClient } from './httpClient'
import type { HttpClientConfig } from './config'
import type { ApiResponse } from './types'

export * from './types'
export * from './eventBus'
export * from './config'

// 默认全局实例
export const http = new HttpClient()

// 创建自定义实例
export const createHttp = (config: Partial<HttpClientConfig> = {}) => new HttpClient(config)

// 通用请求方法 - 类似 axios 的使用方式
export const request = <R = any>(config: {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  params?: any
  headers?: any
  skipAuth?: boolean
  [key: string]: any
}): Promise<ApiResponse<R>> => {
  return http.request<R>(config)
}

// 公开请求方法（无需认证）
export const publicRequest = <R = any>(config: {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  params?: any
  headers?: any
  [key: string]: any
}): Promise<ApiResponse<R>> => {
  return request<R>({ ...config, skipAuth: true })
}