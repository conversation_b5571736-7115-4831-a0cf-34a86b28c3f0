/* 页面全局状态与初始化调度 */
import { defineStore } from 'pinia'
import { useAuthStore } from './auth'
import { useUserStore } from './user'
import { useGameStore } from './game'
import { useThemeStore } from './theme'
import { homeApi, configApi } from '@/api'
import type { UserBindingInfo } from '@/api/home/<USER>'
import type { GameBasic } from '@/api/config/types'

export const useAppStore = defineStore('app', {
  state: () => ({
    /** 是否已完成首屏必需数据加载 */
    isReady: false,
    /** 当前是否正在初始化 */
    initializing: false,
  }),

  actions: {
    /**
     * 首屏初始化：
     * 1. 校验/刷新 token
     * 2. 并行拉取游戏列表、主题配置、用户绑定信息
     * 3. 更新对应 Store，再标记 ready
     */
    async init() {
      if (this.initializing || this.isReady) return
      this.initializing = true

      try {
        const authStore = useAuthStore()
        const gameStore = useGameStore()
        const themeStore = useThemeStore()

        // 1. 保证 token 可用
        await authStore.getValidAccessToken()

        // 2. 并行获取业务数据
        const [gameBasicsRes, userBindingsRes, themeConfigRes] =
					await Promise.all([
						// 获取游戏基础配置
						configApi.queryConfig({
							configType: 'game',
							configKey: 'systemGameList',
						}),
						// 获取用户绑定信息
						homeApi.getUserBindings(),
						// 获取主题配置
						configApi.queryConfig({
							configType: 'theme',
							configKey: 'homeThemes',
						}),
					])

        // 3. 处理游戏基础配置
        if (gameBasicsRes.data && Array.isArray(gameBasicsRes.data)) {
          const games = gameBasicsRes.data.map((item: GameBasic) => ({
            id: item.gameId,
            name: item.gameName,
            imageUrl: item.squareLogo,
            rectangleLogo: item.rectangleLogo
          }))
          gameStore.setGames(games)
        }

        // 4. 处理用户绑定信息
        if (userBindingsRes.data && Array.isArray(userBindingsRes.data)) {
          const bindings = userBindingsRes.data.map((item: UserBindingInfo) => ({
            uid: item.uid,
            name: item.name,
            avatar: item.avatar,
            gameId: Number(item.gameId),
            gameName: item.gameName,
            isActive: item.isActive,
            bindingId: item.bindingId
          }))

          gameStore.setBindings(bindings)
        }

        // 5. 处理主题配置
        if (themeConfigRes.data) {
          themeStore.setHomeConfig(themeConfigRes.data)

          // 设置当前激活主题
          if (gameStore.activeBinding) {
            themeStore.setActiveTheme(gameStore.activeBinding.gameId)
          }
        }

        // 6. 标记就绪
        this.isReady = true
        console.log('[AppStore] 初始化完成')
      }
      catch (error) {
        console.error('[AppStore] 初始化失败:', error)
        // 即使失败也标记为就绪，避免阻塞页面
        this.isReady = true
      }
      finally {
        this.initializing = false
      }
    },

    /**
     * 重置应用状态
     */
    reset() {
      this.isReady = false
      this.initializing = false
    }
  },
})