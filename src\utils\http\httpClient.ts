/* httpNew 核心 HttpClient 类 */
// 功能: 基于 axios 实现的网络请求封装, 内置 Token 注入、刷新、错误处理、事件派发

import axios, { AxiosError, AxiosInstance, AxiosResponse, type AxiosRequestConfig } from 'axios'
import { mergeConfig, type HttpClientConfig } from './config'
import { httpEventBus } from './eventBus'
import type { ApiResponse } from './types'
import { useAuthStore } from '@/stores/auth'

interface InternalRequestConfig extends AxiosRequestConfig {
  /** 标记是否已经自动重试 */
  __isRetry?: boolean
  /** 请求开始时间 */
  __startAt?: number
  /** 是否跳过 Token 注入与刷新 */
  skipAuth?: boolean
  /** 重试次数 */
  __retryCount?: number
  /** 最大重试次数 */
  __maxRetries?: number
  /** 标记是否已经进行过Token预检查 */
  __tokenPreChecked?: boolean
}

type RequestCfg = AxiosRequestConfig & { skipAuth?: boolean }

export class HttpClient {
  private axios: AxiosInstance
  private cfg: HttpClientConfig
  /** 刷新 Token 的共享 Promise, 保证同一时间只执行一次 */
  private refreshPromise: Promise<string> | null = null
  /** Token 预检查的共享 Promise, 避免并发请求重复检查 */
  private tokenCheckPromise: Promise<boolean> | null = null

  constructor(customCfg: Partial<HttpClientConfig> = {}) {
    this.cfg = mergeConfig(customCfg)
    this.axios = axios.create(this.cfg.net)
    this.setupInterceptors()
  }

  /* ---------------- 外部请求方法 ---------------- */
  get<R = any>(url: string, config?: RequestCfg): Promise<ApiResponse<R>> {
    return this.request<R>({ ...config, url, method: 'GET' })
  }
  post<R = any, D = any>(url: string, data?: D, config?: RequestCfg): Promise<ApiResponse<R>> {
    return this.request<R>({ ...config, url, method: 'POST', data })
  }
  put<R = any, D = any>(url: string, data?: D, config?: RequestCfg): Promise<ApiResponse<R>> {
    return this.request<R>({ ...config, url, method: 'PUT', data })
  }
  delete<R = any>(url: string, config?: RequestCfg): Promise<ApiResponse<R>> {
    return this.request<R>({ ...config, url, method: 'DELETE' })
  }
  
  async request<R = any>(config: RequestCfg): Promise<ApiResponse<R>> {
    // 请求前进行Token预检查
    if (!config.skipAuth) {
      await this.preCheckToken()
      console.log('preCheckToken', this.tokenCheckPromise)
    }
    return this.axios.request<ApiResponse<R>>(config) as unknown as Promise<ApiResponse<R>>
  }

  /* --------- 无需认证的便捷方法 (自动 skipAuth) --------- */
  publicGet<R = any>(url: string, config?: AxiosRequestConfig) {
    return this.get<R>(url, { ...config, skipAuth: true })
  }
  publicPost<R = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig) {
    return this.post<R, D>(url, data, { ...config, skipAuth: true })
  }
  publicPut<R = any, D = any>(url: string, data?: D, config?: AxiosRequestConfig) {
    return this.put<R, D>(url, data, { ...config, skipAuth: true })
  }
  publicDelete<R = any>(url: string, config?: AxiosRequestConfig) {
    return this.delete<R>(url, { ...config, skipAuth: true })
  }

  /* ---------------- Token 预检查机制 ---------------- */
  
  /**
   * 请求前检查Token有效性，必要时主动刷新
   * 使用共享Promise避免并发请求重复检查
   */
  private async preCheckToken(): Promise<boolean> {
    if (this.tokenCheckPromise) {
      return this.tokenCheckPromise
    }

    this.tokenCheckPromise = this._doPreCheckToken().finally(() => {
      this.tokenCheckPromise = null
    })

    return this.tokenCheckPromise
  }

  private async _doPreCheckToken(): Promise<boolean> {
    const authStore = useAuthStore()
    
    // 如果没有Token，直接返回false
    if (!authStore.accessToken && !authStore.refreshToken) {
      this.emitTokenEvent('no_token')
      return false
    }

    // 如果accessToken未过期，直接返回true
    if (!authStore.isAccessTokenExpired) {
      return true
    }

    // 如果refreshToken已过期，触发重新认证事件
    if (authStore.isRefreshTokenExpired) {
      this.emitTokenEvent('refresh_expired')
      return false
    }

    // accessToken过期但refreshToken未过期，主动刷新
    try {
      this.emitTokenEvent('refreshing')
      await this.refreshToken()
      this.emitTokenEvent('refreshed')
      return true
    } catch (error) {
      console.error('Token预检查刷新失败:', error)
      this.emitTokenEvent('refresh_failed')
      return false
    }
  }

  /**
   * 发送Token相关事件，供业务层监听处理
   */
  private emitTokenEvent(type: 'no_token' | 'refresh_expired' | 'refreshing' | 'refreshed' | 'refresh_failed') {
    const authStore = useAuthStore()
    httpEventBus.emit('token:status', {
      type,
      hasAccessToken: !!authStore.accessToken,
      hasRefreshToken: !!authStore.refreshToken,
      isAccessTokenExpired: authStore.isAccessTokenExpired,
      isRefreshTokenExpired: authStore.isRefreshTokenExpired,
      timestamp: Date.now()
    })
  }

  /* ---------------- 拦截器 ---------------- */
  private setupInterceptors() {
    this.axios.interceptors.request.use(
      async (cfg) => {
        const authStore = useAuthStore()
        const token = authStore.accessToken
        
        if (token && cfg.skipAuth !== true) {
          cfg.headers = cfg.headers || {}
          cfg.headers[this.cfg.token.headerKey] = `${this.cfg.token.prefix}${token}`
        }
        
        ;(cfg as InternalRequestConfig).__startAt = Date.now()
        httpEventBus.emit('request:start', {
          url: cfg.url || '',
          method: (cfg.method || 'GET').toUpperCase(),
          data: cfg.data,
          startAt: Date.now(),
        })
        return cfg
      },
      (error) => Promise.reject(error),
    )

    this.axios.interceptors.response.use(
      (res) => this.handleResponse(res),
      (err) => this.handleError(err),
    )
  }

  private handleResponse<T = any>(res: AxiosResponse<any>): ApiResponse<T> | Promise<ApiResponse<T>> {
    const cfg = res.config as InternalRequestConfig
    const duration = Date.now() - (cfg.__startAt || Date.now())
    const data: ApiResponse = this.formatResponse(res.data)

    httpEventBus.emit('request:success', {
      url: res.config.url || '',
      method: (res.config.method || 'GET').toUpperCase(),
      data: res.config.data,
      startAt: cfg.__startAt || Date.now(),
      response: data,
      duration,
    })

    // 业务码检查
    if (!this.cfg.business.successCodes.includes(data.code)) {
      // token 失效码 - 作为兜底机制
      if (this.cfg.business.tokenInvalidCodes.includes(data.code) && !(res.config as InternalRequestConfig).skipAuth) {
        return this.retryAfterRefresh(res, data.code)
      }
      // 业务错误
      this.emitBusinessError(res, data)
      return Promise.reject(data)
    }

    return data
  }

  private async handleError(error: AxiosError) {
    const cfg = error.config as InternalRequestConfig | undefined

    if (!error.response) {
      // 网络错误，支持重试
      if (this.shouldRetry(cfg, 'network')) {
        return this.retryRequest(error.config as InternalRequestConfig, 'network')
      }
      this.emitError('network', error, cfg)
      return Promise.reject(error)
    }

    // 超时错误，支持重试
    if (error.code === 'ECONNABORTED' || /timeout|etimedout/i.test(error.message)) {
      if (this.shouldRetry(cfg, 'timeout')) {
        return this.retryRequest(error.config as InternalRequestConfig, 'timeout')
      }
      this.emitError('timeout', error, cfg)
      return Promise.reject(error)
    }

    // 401 未授权 - 作为兜底机制
    if (error.response.status === 401 && !(error.config as InternalRequestConfig)?.skipAuth) {
      return this.retryAfterRefresh(error.response, 401)
    }

    // 其他 http 错误
    this.emitError('http', error, cfg)
    return Promise.reject(error)
  }

  /* ---------------- 私有工具 ---------------- */
  private formatResponse(data: any): ApiResponse {
    if (data && typeof data === 'object' && 'code' in data && 'data' in data) {
      return data as ApiResponse
    }
    return { code: 0, data, message: '' }
  }

  private emitError(type: 'network' | 'http' | 'business' | 'timeout', error: AxiosError, cfg?: InternalRequestConfig, code?: number, message?: string) {
    httpEventBus.emit('request:error', {
      url: cfg?.url || error.config?.url || '',
      method: (cfg?.method || error.config?.method || 'GET').toUpperCase(),
      data: cfg?.data,
      startAt: cfg?.__startAt || Date.now(),
      type,
      status: error.response?.status,
      code,
      message: message || error.message,
      error,
    })
  }

  private emitBusinessError(res: AxiosResponse, data: ApiResponse) {
    const cfg = res.config as InternalRequestConfig
    const fallbackMsg = this.cfg.business.codeMessage?.[data.code] || this.cfg.errorMessage.default
    const axiosErr = new AxiosError(data.message || fallbackMsg, undefined, res.config, res.request, res)
    this.emitError('business', axiosErr, cfg, data.code, data.message || fallbackMsg)
  }

  /**
   * 触发 Token 刷新并重试请求（兜底机制）
   */
  private async retryAfterRefresh(originalResponse: AxiosResponse, tokenCode: number) {
    const originalRequest = originalResponse.config as InternalRequestConfig

    if (originalRequest.__isRetry) {
      // 已经重试过仍失败，强制登出
      this.forceLogout()
      return Promise.reject(originalResponse)
    }

    try {
      const newToken = await this.refreshToken()
      // 刷新成功，重试原请求
      originalRequest.__isRetry = true
      originalRequest.headers = {
        ...(originalRequest.headers || {}),
        [this.cfg.token.headerKey]: `${this.cfg.token.prefix}${newToken}`,
      }
      return this.axios(originalRequest)
    } catch (e) {
      // 刷新失败，登出
      this.forceLogout()
      return Promise.reject(e)
    }
  }

  /** 刷新 Token，保证并发时只请求一次 */
  private async refreshToken(): Promise<string> {
    if (!this.refreshPromise) {
      const authStore = useAuthStore()
      this.refreshPromise = authStore.refreshAccessToken().finally(() => {
        this.refreshPromise = null
      })
    }
    return this.refreshPromise
  }

  /** 强制登出并派发事件，让业务层跳转到登录授权页 */
  private forceLogout() {
    const authStore = useAuthStore()
    authStore.logout()
    httpEventBus.emit('auth:logout', { relogin: true })
  }

  /** 判断是否应该重试 */
  private shouldRetry(cfg: InternalRequestConfig | undefined, errorType: 'network' | 'timeout'): boolean {
    if (!cfg) return false
    
    // 使用配置文件中的重试策略
    const maxRetries = cfg.__maxRetries ?? this.cfg.retry.maxRetries
    const retryCount = cfg.__retryCount ?? 0
    
    // 检查是否为可重试的请求方法
    const isRetryableMethod = this.cfg.retry.retryableMethods.includes(cfg.method?.toUpperCase() || 'GET')
    
    return isRetryableMethod && retryCount < maxRetries
  }

  /** 重试请求 */
  private async retryRequest(cfg: InternalRequestConfig, reason: 'network' | 'timeout' = 'network'): Promise<any> {
    cfg.__retryCount = (cfg.__retryCount || 0) + 1
    
    // 发送重试事件
    httpEventBus.emit('request:retry', {
      url: cfg.url || '',
      method: (cfg.method || 'GET').toUpperCase(),
      data: cfg.data,
      startAt: cfg.__startAt || Date.now(),
      retryCount: cfg.__retryCount,
      reason
    })
    
    // 使用配置文件中的延迟策略
    const baseDelay = this.cfg.retry.retryDelay
    const maxDelay = this.cfg.retry.maxRetryDelay
    const delay = Math.min(baseDelay * Math.pow(2, cfg.__retryCount - 1), maxDelay)
    
    await new Promise(resolve => setTimeout(resolve, delay))
    
    return this.axios(cfg)
  }
} 