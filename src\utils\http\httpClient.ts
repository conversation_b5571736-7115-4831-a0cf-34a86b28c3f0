/* httpNew 核心 HttpClient 类 */
// 功能: 基于 axios 实现的网络请求封装, 内置 Token 注入、刷新、错误处理、事件派发

import axios, { AxiosError, AxiosInstance, AxiosResponse, type AxiosRequestConfig } from 'axios'
import { mergeConfig, type HttpClientConfig } from './config'
import { httpEventBus } from './eventBus'
import type { ApiResponse } from './types'
import { useAuthStore } from '@/stores/auth'

interface InternalRequestConfig extends AxiosRequestConfig {
  /** 标记是否已经自动重试 */
  __isRetry?: boolean
  /** 请求开始时间 */
  __startAt?: number
  /** 是否跳过 Token 注入与刷新 */
  skipAuth?: boolean
  /** 重试次数 */
  __retryCount?: number
  /** 最大重试次数 */
  __maxRetries?: number
  /** 标记是否已经进行过Token预检查 */
  __tokenPreChecked?: boolean
}

type RequestCfg = AxiosRequestConfig & { skipAuth?: boolean }

export class HttpClient {
  private axios: AxiosInstance
  private cfg: HttpClientConfig
  /** 刷新 Token 的共享 Promise, 保证同一时间只执行一次 */
  private refreshPromise: Promise<string> | null = null

  constructor(customCfg: Partial<HttpClientConfig> = {}) {
    this.cfg = mergeConfig(customCfg)
    this.axios = axios.create(this.cfg.net)
    this.setupInterceptors()
  }

  /* ---------------- 统一请求方法 ---------------- */
  async request<R = any>(config: RequestCfg): Promise<ApiResponse<R>> {
    // 简化：只在需要认证时进行token检查，移除复杂的预检查机制
    if (!config.skipAuth) {
      await this.ensureValidToken()
    }
    return this.axios.request<ApiResponse<R>>(config) as unknown as Promise<ApiResponse<R>>
  }

  /* ---------------- 简化的Token管理 ---------------- */

  /**
   * 确保有有效的访问令牌
   * 简化逻辑：只在真正需要时才刷新，避免过度检查
   */
  private async ensureValidToken(): Promise<void> {
    const authStore = useAuthStore()

    // 如果没有任何token，直接返回让请求失败，由拦截器处理
    if (!authStore.accessToken && !authStore.refreshToken) {
      return
    }

    // 如果accessToken未过期，直接使用
    if (!authStore.isAccessTokenExpired && authStore.accessToken) {
      return
    }

    // 如果refreshToken过期，清除状态让拦截器处理
    if (authStore.isRefreshTokenExpired) {
      authStore.logout()
      return
    }

    // 需要刷新token
    try {
      await this.refreshToken()
    } catch (error) {
      // 刷新失败，静默清除状态让拦截器处理
      authStore.logout()
    }
  }

  /* ---------------- 简化的Token刷新机制 ---------------- */

  /** 刷新 Token，保证并发时只请求一次 */
  private async refreshToken(): Promise<string> {
    if (!this.refreshPromise) {
      const authStore = useAuthStore()
      this.refreshPromise = authStore.refreshAccessToken().finally(() => {
        this.refreshPromise = null
      })
    }
    return this.refreshPromise
  }

  /* ---------------- 拦截器 ---------------- */
  private setupInterceptors() {
    this.axios.interceptors.request.use(
      async (cfg) => {
        const authStore = useAuthStore()
        const token = authStore.accessToken
        
        if (token && cfg.skipAuth !== true) {
          cfg.headers = cfg.headers || {}
          cfg.headers[this.cfg.token.headerKey] = `${this.cfg.token.prefix}${token}`
        }
        
        ;(cfg as InternalRequestConfig).__startAt = Date.now()
        httpEventBus.emit('request:start', {
          url: cfg.url || '',
          method: (cfg.method || 'GET').toUpperCase(),
          data: cfg.data,
          startAt: Date.now(),
        })
        return cfg
      },
      (error) => Promise.reject(error),
    )

    this.axios.interceptors.response.use(
      (res) => this.handleResponse(res),
      (err) => this.handleError(err),
    )
  }

  private handleResponse<T = any>(res: AxiosResponse<any>): ApiResponse<T> | Promise<ApiResponse<T>> {
    const cfg = res.config as InternalRequestConfig
    const duration = Date.now() - (cfg.__startAt || Date.now())
    const data: ApiResponse = this.formatResponse(res.data)

    httpEventBus.emit('request:success', {
      url: res.config.url || '',
      method: (res.config.method || 'GET').toUpperCase(),
      data: res.config.data,
      startAt: cfg.__startAt || Date.now(),
      response: data,
      duration,
    })

    // 业务码检查
    if (!this.cfg.business.successCodes.includes(data.code)) {
      // token 失效码 - 简化处理，直接触发登出
      if (this.cfg.business.tokenInvalidCodes.includes(data.code) && !(res.config as InternalRequestConfig).skipAuth) {
        this.forceLogout()
        return Promise.reject(data)
      }
      // 业务错误
      this.emitBusinessError(res, data)
      return Promise.reject(data)
    }

    return data
  }

  private async handleError(error: AxiosError) {
    const cfg = error.config as InternalRequestConfig | undefined

    if (!error.response) {
      // 网络错误，支持重试
      if (this.shouldRetry(cfg, 'network')) {
        return this.retryRequest(error.config as InternalRequestConfig, 'network')
      }
      this.emitError('network', error, cfg)
      return Promise.reject(error)
    }

    // 超时错误，支持重试
    if (error.code === 'ECONNABORTED' || /timeout|etimedout/i.test(error.message)) {
      if (this.shouldRetry(cfg, 'timeout')) {
        return this.retryRequest(error.config as InternalRequestConfig, 'timeout')
      }
      this.emitError('timeout', error, cfg)
      return Promise.reject(error)
    }

    // 401 未授权 - 简化处理，直接触发登出
    if (error.response.status === 401 && !(error.config as InternalRequestConfig)?.skipAuth) {
      this.forceLogout()
      return Promise.reject(error)
    }

    // 其他 http 错误
    this.emitError('http', error, cfg)
    return Promise.reject(error)
  }

  /* ---------------- 私有工具 ---------------- */
  private formatResponse(data: any): ApiResponse {
    if (data && typeof data === 'object' && 'code' in data && 'data' in data) {
      return data as ApiResponse
    }
    return { code: 0, data, message: '' }
  }

  private emitError(type: 'network' | 'http' | 'business' | 'timeout', error: AxiosError, cfg?: InternalRequestConfig, code?: number, message?: string) {
    httpEventBus.emit('request:error', {
      url: cfg?.url || error.config?.url || '',
      method: (cfg?.method || error.config?.method || 'GET').toUpperCase(),
      data: cfg?.data,
      startAt: cfg?.__startAt || Date.now(),
      type,
      status: error.response?.status,
      code,
      message: message || error.message,
      error,
    })
  }

  private emitBusinessError(res: AxiosResponse, data: ApiResponse) {
    const cfg = res.config as InternalRequestConfig
    const fallbackMsg = this.cfg.business.codeMessage?.[data.code] || this.cfg.errorMessage.default
    const axiosErr = new AxiosError(data.message || fallbackMsg, undefined, res.config, res.request, res)
    this.emitError('business', axiosErr, cfg, data.code, data.message || fallbackMsg)
  }

  /** 强制登出并派发事件，让业务层跳转到登录授权页 */
  private forceLogout() {
    const authStore = useAuthStore()
    authStore.logout()
    httpEventBus.emit('auth:logout', { relogin: true })
  }

  /** 判断是否应该重试 */
  private shouldRetry(cfg: InternalRequestConfig | undefined, errorType: 'network' | 'timeout'): boolean {
    if (!cfg) return false
    
    // 使用配置文件中的重试策略
    const maxRetries = cfg.__maxRetries ?? this.cfg.retry.maxRetries
    const retryCount = cfg.__retryCount ?? 0
    
    // 检查是否为可重试的请求方法
    const isRetryableMethod = this.cfg.retry.retryableMethods.includes(cfg.method?.toUpperCase() || 'GET')
    
    return isRetryableMethod && retryCount < maxRetries
  }

  /** 重试请求 */
  private async retryRequest(cfg: InternalRequestConfig, reason: 'network' | 'timeout' = 'network'): Promise<any> {
    cfg.__retryCount = (cfg.__retryCount || 0) + 1
    
    // 发送重试事件
    httpEventBus.emit('request:retry', {
      url: cfg.url || '',
      method: (cfg.method || 'GET').toUpperCase(),
      data: cfg.data,
      startAt: cfg.__startAt || Date.now(),
      retryCount: cfg.__retryCount,
      reason
    })
    
    // 使用配置文件中的延迟策略
    const baseDelay = this.cfg.retry.retryDelay
    const maxDelay = this.cfg.retry.maxRetryDelay
    const delay = Math.min(baseDelay * Math.pow(2, cfg.__retryCount - 1), maxDelay)
    
    await new Promise(resolve => setTimeout(resolve, delay))
    
    return this.axios(cfg)
  }
} 