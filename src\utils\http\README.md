# HTTP 工具使用指南

## 概述

本项目提供了基于 axios 的 HTTP 客户端封装，内置了完整的 Token 管理、错误处理、重试机制和事件系统。

## 核心特性

### 1. Token 管理机制

#### 双重保障的 Token 刷新策略

**第一层：请求前主动检查（推荐）**
- 在每次 API 请求前自动检查 Token 有效性
- 如果 accessToken 过期但 refreshToken 未过期，主动刷新
- 避免不必要的网络请求失败

**第二层：响应拦截器兜底**
- 当服务器返回 401 状态码或特定业务码时触发刷新
- 作为兜底机制，确保 Token 失效时能自动恢复

#### Token 预检查流程

```typescript
// 请求前检查流程
1. 检查是否有 Token
   ├─ 无 Token → 触发 'no_token' 事件
   └─ 有 Token → 继续检查

2. 检查 accessToken 是否过期
   ├─ 未过期 → 直接使用
   └─ 已过期 → 继续检查

3. 检查 refreshToken 是否过期
   ├─ 已过期 → 触发 'refresh_expired' 事件
   └─ 未过期 → 主动刷新 accessToken

4. 刷新 accessToken
   ├─ 成功 → 触发 'refreshed' 事件
   └─ 失败 → 触发 'refresh_failed' 事件
```

### 2. 事件系统

#### 支持的事件类型

```typescript
// 请求相关事件
'request:start'    // 请求开始
'request:success'  // 请求成功
'request:error'    // 请求错误
'request:retry'    // 请求重试

// 认证相关事件
'auth:logout'      // 用户登出
'token:status'     // Token 状态变化
```

#### Token 状态事件

```typescript
interface TokenStatusEvent {
  type: 'no_token' | 'refresh_expired' | 'refreshing' | 'refreshed' | 'refresh_failed'
  hasAccessToken: boolean
  hasRefreshToken: boolean
  isAccessTokenExpired: boolean
  isRefreshTokenExpired: boolean
  timestamp: number
}
```

### 3. 错误处理

#### 自动重试机制

- **网络错误**：自动重试，支持指数退避
- **超时错误**：自动重试，可配置重试次数
- **Token 失效**：自动刷新并重试请求

#### 业务错误处理

```typescript
// 配置业务错误码
business: {
  successCodes: [0],
  tokenInvalidCodes: [401001, 401002, 500002],
  codeMessage: {
    401001: 'Token无效',
    401002: 'Token已过期',
    500002: 'refresh_token无效'
  }
}
```

## 使用方法

### 1. 基础使用

```typescript
import { http, request } from '@/utils/http'

// 使用默认实例
const response = await http.get('/api/users')

// 使用 request 函数
const response = await request({
  url: '/api/users',
  method: 'GET'
})

// 无需认证的请求
const response = await http.publicGet('/api/public/data')
```

### 2. Token 监控

```typescript
import { useTokenMonitor } from '@/composables/useTokenMonitor'

// 在组件中使用
const { isRefreshing, lastTokenEvent, checkTokenStatus } = useTokenMonitor()

// 监听 Token 刷新状态
watch(isRefreshing, (refreshing) => {
  if (refreshing) {
    // 显示加载状态
    showLoading()
  } else {
    // 隐藏加载状态
    hideLoading()
  }
})
```

### 3. 事件监听

```typescript
import { httpEventBus } from '@/utils/http'

// 监听 Token 状态变化
httpEventBus.on('token:status', (event) => {
  console.log('Token 状态:', event.type)
  
  switch (event.type) {
    case 'refreshing':
      // 显示刷新提示
      break
    case 'refreshed':
      // 刷新成功
      break
    case 'refresh_failed':
      // 刷新失败，跳转登录
      break
  }
})

// 监听请求错误
httpEventBus.on('request:error', (event) => {
  console.error('请求错误:', event.message)
})
```

### 4. 自定义配置

```typescript
import { createHttp } from '@/utils/http'

// 创建自定义实例
const customHttp = createHttp({
  net: {
    baseURL: 'https://api.example.com',
    timeout: 10000
  },
  business: {
    successCodes: [0, 200],
    tokenInvalidCodes: [401, 403]
  },
  retry: {
    maxRetries: 3,
    retryDelay: 1000
  }
})
```

## 最佳实践

### 1. Token 管理

- ✅ **推荐**：使用请求前预检查机制
- ✅ **推荐**：监听 Token 状态事件进行 UI 反馈
- ❌ **避免**：手动调用刷新接口
- ❌ **避免**：在业务代码中直接处理 Token 过期

### 2. 错误处理

- ✅ **推荐**：使用事件系统统一处理错误
- ✅ **推荐**：配置合适的重试策略
- ❌ **避免**：在每个请求中重复处理错误

### 3. 性能优化

- ✅ **推荐**：使用共享 Promise 避免重复刷新
- ✅ **推荐**：合理配置超时时间
- ❌ **避免**：频繁创建 HTTP 实例

### 4. 调试技巧

```typescript
// 开启调试模式
import { httpEventBus } from '@/utils/http'

// 监听所有事件
httpEventBus.on('*', (event) => {
  console.log('HTTP Event:', event)
})

// 手动检查 Token 状态
const { checkTokenStatus } = useTokenMonitor()
checkTokenStatus()
```

## 配置说明

### 完整配置示例

```typescript
const config = {
  net: {
    baseURL: 'https://api.example.com',
    timeout: 15000,
    withCredentials: true
  },
  business: {
    successCodes: [0],
    tokenInvalidCodes: [401001, 401002, 500002],
    codeMessage: {
      401001: 'Token无效',
      401002: 'Token已过期'
    }
  },
  token: {
    headerKey: 'Authorization',
    prefix: 'Bearer '
  },
  retry: {
    maxRetries: 2,
    retryDelay: 1000,
    maxRetryDelay: 5000,
    retryableMethods: ['GET', 'HEAD', 'OPTIONS']
  },
  errorMessage: {
    default: '未知错误',
    network: '网络连接异常',
    timeout: '请求超时'
  }
}
```

## 迁移指南

### 从旧版本升级

1. **Token 刷新机制**：现在会自动在请求前检查，无需手动处理
2. **事件系统**：使用 `httpEventBus` 替代原有的回调方式
3. **错误处理**：统一通过事件系统处理，减少重复代码

### 兼容性

- 保持与现有 API 的兼容性
- 新增功能为可选，不影响现有代码
- 提供渐进式升级路径