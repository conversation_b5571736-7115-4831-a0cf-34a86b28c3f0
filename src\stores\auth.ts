import { defineStore } from 'pinia'
import { weunitAuthApi, type TokenInfo } from '@/api/auth'

export enum LoginStatus {
  NOT_LOGGED = 0,
  AUTH_IN_PROGRESS = 1,
  LOGGED_IN = 2,
}

// 认证存储
export const useAuthStore = defineStore('auth', {
  state: () => ({
    loginStatus: LoginStatus.NOT_LOGGED,
    needsReauth: false,
    // Token 相关存储
    accessToken: '',
    refreshToken: '',
    accessTokenExpiresAt: 0,
    refreshTokenExpiresAt: 0
  }),

  getters: {
    // 检查用户是否已登录
    isLoggedIn(): boolean {
      return this.loginStatus === LoginStatus.LOGGED_IN
    },
    
    // 判断访问令牌是否过期
    isAccessTokenExpired(): boolean {
      return Date.now() >= this.accessTokenExpiresAt
    },
    
    // 判断刷新令牌是否过期
    isRefreshTokenExpired(): boolean {
      return Date.now() >= this.refreshTokenExpiresAt
    },
    
    /**
     * 检查是否仍有可用令牌
     * 1. accessToken 未过期 → true
     * 2. 否则 access 已过期但 refreshToken 仍未过期 → 可刷新 → true
     * 3. 两者皆过期 → false
     */
    hasValidToken(): boolean {
      if (!this.accessToken && !this.refreshToken) return false
      if (!this.isAccessTokenExpired) return true
      return !this.isRefreshTokenExpired
    },
  },

  actions: {
    setLoginStatus(loginStatus: LoginStatus) {
      this.loginStatus = loginStatus
    },
    
    setNeedsReauth(needsReauth: boolean) {
      this.needsReauth = needsReauth
    },
    
    // 设置Token相关信息
    setTokenInfo(tokenInfo: TokenInfo) {
      const now = Date.now()
      
      this.accessToken = tokenInfo.accessToken
      this.refreshToken = tokenInfo.refreshToken
      // 将过期秒数转换为时间戳
      this.accessTokenExpiresAt = now + tokenInfo.accessTokenExpiresIn * 1000
      this.refreshTokenExpiresAt = now + tokenInfo.refreshTokenExpiresIn * 1000
    },
    
    // 清除Token
    clearTokens() {
      this.accessToken = ''
      this.refreshToken = ''
      this.accessTokenExpiresAt = 0
      this.refreshTokenExpiresAt = 0
    },
    
    // 刷新访问令牌
    async refreshAccessToken() {
      try {
        if (!this.refreshToken) {
          throw new Error('刷新令牌不存在')
        }
        
        const response = await weunitAuthApi.refreshToken(this.refreshToken)
        this.setTokenInfo(response.data)
        return this.accessToken
      } catch (error) {
        console.error('刷新令牌失败:', error)
        // 刷新失败，清除token并标记需要重新认证
        this.clearTokens()
        this.setNeedsReauth(true)
        this.setLoginStatus(LoginStatus.NOT_LOGGED)
        throw error
      }
    },
    
    // 获取有效的访问令牌（如果过期会尝试刷新）
    async getValidAccessToken(): Promise<string> {
      // 如果访问令牌未过期，直接返回
      if (!this.isAccessTokenExpired && this.accessToken) {
        return this.accessToken
      }
      
      // 如果刷新令牌过期，抛出错误
      if (this.isRefreshTokenExpired) {
        this.setNeedsReauth(true)
        this.setLoginStatus(LoginStatus.NOT_LOGGED)
        throw new Error('认证已过期，需要重新登录')
      }
      
      // 尝试刷新访问令牌
      return await this.refreshAccessToken()
    },
    
    // 用户登录
    async handleLogin(data: { code: string }) {
      try {
        // 调用认证登录接口
        const res = await weunitAuthApi.login({
          code: data.code,
          appId: import.meta.env.VITE_APP_WECHAT_APP_ID,
          platformType: 'WECHAT'
        })
        
        // 存储token信息
        this.setTokenInfo(res.data)
        
        // 设置登录状态
        this.setLoginStatus(LoginStatus.LOGGED_IN)
        
        return res.data
      } catch (err) {
        this.setLoginStatus(LoginStatus.NOT_LOGGED)
        throw err
      }
    },
    
    // 注销登录
    logout() {
      this.clearTokens()
      this.setLoginStatus(LoginStatus.NOT_LOGGED)
    }
  },

  persist: {
    enabled: true,
    storage: localStorage,
    key: '__giant__auth__info__',
  },
}) 